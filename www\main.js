// main.js
// Optimierte Version mit GPS-Sprungschutz, Coalescing, Batch-Processing (100er Chunks) und Preload-Cache

import { config } from './config.js';
import { logger } from './utils/logger.js';
import { gameState } from './state/game-state.js';
import { MapRenderer } from './services/map-renderer.js';
import { playerRenderer } from './services/player-renderer.js';
import { PokemonSpawner, calculateAzimuth } from './services/pokemon-spawner.js';
import { distanceMeters } from './services/utils/distance.js';
import { fabManager } from './ui/FabManager.js';
import { storageService } from './storage/storage-service.js';
import { addEncounter } from './storage/encountersStorage.js';
import { startWatchPosition, requestLocationPermissions } from './capacitor/geolocation.js';
import { keepScreenAwake } from './capacitor/keep-awake.js';
import { registerMainMapBackButtonHandler } from './capacitor/app.js';
import { pokemonManager } from './services/pokemon-manager.js';
import {
  initializeTimeEvents,
  removePokemonFromStorage,
  syncGameStateWithStorage,
  clearSpawnsFromStorage
} from './capacitor/time-events.js';
import { trainerSpawner } from './services/trainer-spawner.js';
import { gpsMock } from './services/gps-mock.js';

// =========================
// Global error handling
// =========================
window.addEventListener('error', function(event) {
  logger.error(`Unhandled error: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`, event.error);
});

window.addEventListener('unhandledrejection', function(event) {
  logger.error('Unhandled promise rejection:', event.reason);
});

// =========================
// Core app state
// =========================
const appState = {
  isInitializing: false,
  isInitialized: false,
  gpsWatchActive: false,
  currentMapRenderer: null,
  currentPokemonSpawner: null
};

// =========================
// Mutex light
// =========================
class AsyncMutex {
  constructor() { this.locked = false; this.queue = []; }
  async acquire() {
    return new Promise((resolve) => {
      if (!this.locked) { this.locked = true; resolve(); }
      else { this.queue.push(resolve); }
    });
  }
  release() {
    if (this.queue.length > 0) this.queue.shift()();
    else this.locked = false;
  }
}
const spawnMutex = new AsyncMutex();
const trainerMutex = new AsyncMutex();
const storageMutex = new AsyncMutex();
const locationMutex = new AsyncMutex();

// =========================
// Utils: debounce/throttle
// =========================
function debounce(fn, wait) {
  let t; return (...args) => { clearTimeout(t); t = setTimeout(() => fn(...args), wait); };
}
function throttle(fn, limit) {
  let inThrottle = false;
  return (...args) => {
    if (!inThrottle) { inThrottle = true; fn(...args); setTimeout(() => { inThrottle = false; }, limit); }
  };
}

// =========================
// Anti-jitter & spawn gating
// =========================
const gpsFilter = {
  lastAccepted: null, // { lat, lng, t }
  lastRaw: null,
  lastSpeedMps: 0,
  // Konfiguration
  // Reject, wenn implizite Geschwindigkeit unrealistisch hoch ist (z.B. > 50km/h = 13.9m/s) ODER
  // Sprung in sehr kurzer Zeit >> Distance Gate
  maxMps: 13.9,
  // Hartes Sprunglimit pro Sekunde: in 1s nicht mehr als 120m
  maxMetersPerSecondGate: 120,
  // Cooldown nach erkanntem Sprung (ignoriere Spawn-Trigger für X Sekunden)
  jumpCooldownMs: 5000,
  lastJumpAt: 0,
  // weiches Glätten: akzeptiere Positionswechsel < 5m als Rauschen
  minDeltaMeters: 5
};

function evaluateGpsSample(lat, lng, timestampMs) {
  const now = timestampMs || Date.now();
  const current = { lat, lng, t: now };

  if (!gpsFilter.lastAccepted) {
    gpsFilter.lastAccepted = current;
    gpsFilter.lastRaw = current;
    return { accept: true, reason: 'first_fix' };
  }

  const prev = gpsFilter.lastAccepted;
  const dt = Math.max(0.001, (now - prev.t) / 1000);
  const d = distanceMeters(lat, lng, prev.lat, prev.lng);
  const mps = d / dt;
  gpsFilter.lastSpeedMps = mps;

  // sehr kleine Bewegungen als Rauschen behandeln
  if (d < gpsFilter.minDeltaMeters) {
    gpsFilter.lastRaw = current;
    return { accept: false, reason: 'below_noise_threshold' };
  }

  // harter Sprungschutz
  if (mps > gpsFilter.maxMetersPerSecondGate || mps > gpsFilter.maxMps * 2) {
    gpsFilter.lastRaw = current;
    gpsFilter.lastJumpAt = now;
    logger.warn(`[GPS] Hard jump rejected: d=${d.toFixed(1)}m dt=${dt.toFixed(2)}s mps=${mps.toFixed(1)}`);
    return { accept: false, reason: 'hard_jump' };
  }

  // weicher Speed-Check
  if (mps > gpsFilter.maxMps) {
    gpsFilter.lastRaw = current;
    logger.debug(`[GPS] Unrealistic speed rejected: ${mps.toFixed(1)}m/s`);
    return { accept: false, reason: 'too_fast' };
  }

  // alles okay
  gpsFilter.lastAccepted = current;
  gpsFilter.lastRaw = current;
  return { accept: true, reason: 'ok' };
}

function isInJumpCooldown() {
  return Date.now() - gpsFilter.lastJumpAt < gpsFilter.jumpCooldownMs;
}

// =========================
// Spawn coalescing & batching
// =========================
let pokemonSpawnInFlight = false;
let latestSpawnRequest = null;

async function performChunkedSpawns(spawner, lat, lng, reason, totalStandard, totalLanduse) {
  // Pre-load cache once
  try {
    logger.debug('[CACHE] Preloading landuse data...');
    await spawner.loadLanduseDataForArea(lat, lng, spawner.spawnRadius);
  } catch (e) {
    logger.error('[CACHE] Preload failed', e);
  }

  const BATCH_LIMIT = 100;

  // Helper to run a spawn generator in chunks
  async function spawnInChunks(spawnFn, total, label) {
    let remaining = total;
    let produced = 0;
    while (remaining > 0) {
      const take = Math.min(BATCH_LIMIT, remaining);
      try {
        const batch = await spawnFn(take);
        produced += Array.isArray(batch) ? batch.length : 0;
      } catch (err) {
        logger.error(`[SPAWN] ${label} batch failed (${reason})`, err);
        // weiter mit rest, aber abbrechen, wenn systematische Fehler zu erwarten sind
      }
      remaining -= BATCH_LIMIT;
    }
    logger.info(`[SPAWN] ${label} total produced: ${produced} (${reason})`);
  }

  // Define spawn fns that bind lat/lng
  const doStandard = (count) => spawner.spawnRandomPokemons(lat, lng, count);
  const doLanduse = (count) => spawner.spawnLanduseSpecialPokemons(lat, lng, count);

  // If totals not given, take from config
  const standardTarget = typeof totalStandard === 'number' ? totalStandard : config.pokemon.spawnBatchSize;
  const landuseTarget = typeof totalLanduse === 'number' ? totalLanduse : config.pokemon.landuseSpecialBatchSize;

  // Parallel chunking of both categories
  await Promise.all([
    spawnInChunks(doStandard, standardTarget, 'standard'),
    spawnInChunks(doLanduse, landuseTarget, 'landuse')
  ]);

  // Update anchors and render
  gameState.lastSpawnLatLng = { lat, lng };
  if (fabManager && gameState.debugMode) {
    fabManager.updateDebugRadiusCircle();
  }
  if (appState.currentMapRenderer) {
    await appState.currentMapRenderer.renderPokemons(true);
  }
}

async function requestPokemonSpawn(spawner, lat, lng, reason, totals) {
  if (pokemonSpawnInFlight) {
    latestSpawnRequest = { lat, lng, reason, totals };
    logger.debug(`[SPAWN] Coalesced: queued latest (${reason})`);
    return;
  }

  pokemonSpawnInFlight = true;
  try {
    await performChunkedSpawns(
      spawner,
      lat,
      lng,
      reason,
      totals?.standard,
      totals?.landuse
    );
  } catch (e) {
    logger.error(`[SPAWN] Error during performChunkedSpawns (${reason})`, e);
    if (appState.currentMapRenderer) appState.currentMapRenderer.renderPokemons(true);
  } finally {
    pokemonSpawnInFlight = false;
  }

  // process latest, but drop if within trigger distance or jump-cooldown
  if (latestSpawnRequest) {
    const next = latestSpawnRequest;
    latestSpawnRequest = null;

    if (isInJumpCooldown()) {
      logger.debug('[SPAWN] Dropped coalesced request due to jump cooldown');
      return;
    }

    if (!gameState.lastSpawnLatLng ||
        distanceMeters(next.lat, next.lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng) >= config.pokemon.spawnDistanceTrigger) {
      await requestPokemonSpawn(spawner, next.lat, next.lng, `${next.reason}-coalesced`, next.totals);
    } else {
      logger.debug('[SPAWN] Dropped coalesced request (within trigger distance)');
    }
  }
}

// =========================
// Trainer spawning
// =========================
async function handleTrainerSpawning(lat, lng) {
  await trainerMutex.acquire();
  try {
    if (gameState.trainerSpawnInProgress) return;

    let shouldSpawn = false;
    let dist = 0;

    if (!gameState.lastTrainerSpawnAnchor) {
      shouldSpawn = true;
    } else {
      dist = distanceMeters(gameState.lastTrainerSpawnAnchor.lat, gameState.lastTrainerSpawnAnchor.lng, lat, lng);
      if (dist >= config.trainers.spawnDistanceTrigger) shouldSpawn = true;
    }

    if (!shouldSpawn) return;

    gameState.trainerSpawnInProgress = true;
    const previous = [...gameState.trainers];
    try {
      gameState.trainers = [];
      const newTrainers = await trainerSpawner.spawnRandomTrainers(lat, lng, config.trainers.desiredCount);
      newTrainers.forEach(t => gameState.addTrainer(t));
      if (newTrainers.length > 0) gameState.lastTrainerSpawnAnchor = { lat, lng };
      if (appState.currentMapRenderer) await appState.currentMapRenderer.renderTrainers(true);
    } catch (e) {
      logger.error('[TRAINER] spawn failed, restore previous', e);
      gameState.trainers = previous;
    } finally {
      gameState.trainerSpawnInProgress = false;
    }
  } catch (e) {
    logger.error('[TRAINER] critical error', e);
  } finally {
    trainerMutex.release();
  }
}

// =========================
// Location handling
// =========================
const debouncedLocationHandler = debounce(async (e) => {
  await locationMutex.acquire();
  try { await processLocationUpdate(e); }
  catch (err) { logger.error('Location handler error', err); }
  finally { locationMutex.release(); }
}, 500);

let gpsStabilizationTimer = null; // 2-3 Sekunden

async function processLocationUpdate(e) {
  const lat = e?.latlng?.lat ?? e?.latitude ?? e?.coords?.latitude;
  const lng = e?.latlng?.lng ?? e?.longitude ?? e?.coords?.longitude;
  const accuracy = e?.coords?.accuracy;

  if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
    logger.error('Invalid location data', e);
    // unwichtig -> kein Alert
    return;
  }

  // grobe Filterung über Accuracy (optional)
  if (typeof accuracy === 'number' && accuracy > 100) {
    // sehr schlechte Genauigkeit, nur UI updaten, kein Spawn
    logger.debug(`[GPS] High inaccuracy (${accuracy}m), skip spawn decisions this tick`);
  }

  // Anti-jitter evaluator
  const evalRes = evaluateGpsSample(lat, lng, e?.timestamp || Date.now());
  if (!evalRes.accept) {
    // nur UI minimale Updates
    updateUI(lat, lng, e);
    return;
  }
  if (isInJumpCooldown()) {
    logger.debug('[GPS] In jump cooldown - suppress spawn triggers');
  }

  // Update game state
  gameState.lastUserLatLng = { lat, lng };

  // Spawn decision only if not in jump-cooldown
  if (!isInJumpCooldown()) {
    await spawnDecision(lat, lng);
    await handleTrainerSpawning(lat, lng);
  }

  updateUI(lat, lng, e);
}

async function spawnDecision(lat, lng) {
  // Initial spawn: 2–3 Sekunden Stabilisierung
  if (!gameState.lastSpawnLatLng) {
    if (!gameState.loadedFromStorage && !gpsStabilizationTimer) {
      logger.debug('Start GPS stabilization (2.5s)');
      gpsStabilizationTimer = setTimeout(async () => {
        gpsStabilizationTimer = null;
        await requestPokemonSpawn(appState.currentPokemonSpawner, lat, lng, 'initial');
      }, 2500);
    } else if (gameState.loadedFromStorage) {
      gameState.lastSpawnLatLng = { lat, lng };
      if (appState.currentMapRenderer) appState.currentMapRenderer.renderPokemons();
    }
    return;
  }

  const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

  if (gameState.loadedFromStorage) {
    if (appState.currentMapRenderer) appState.currentMapRenderer.renderPokemons();
    gameState.loadedFromStorage = false;
    return;
  }

  if (gameState.gpsTimeoutOccurred) {
    if (dist >= config.pokemon.spawnDistanceTrigger * 2) {
      await requestPokemonSpawn(appState.currentPokemonSpawner, lat, lng, 'gps-timeout');
    }
    gameState.gpsTimeoutOccurred = false;
    return;
  }

  // Normale Bewegung
  if (dist >= config.pokemon.spawnDistanceTrigger) {
    // Nur den letzten Request verarbeiten (Coalescing in requestPokemonSpawn)
    await requestPokemonSpawn(appState.currentPokemonSpawner, lat, lng, 'movement');
  } else {
    if (appState.currentMapRenderer) appState.currentMapRenderer.renderPokemons();
  }
}

function updateUI(lat, lng, e) {
  try {
    const mapRenderer = appState.currentMapRenderer;
    if (!mapRenderer) return;

    mapRenderer.updateCatchRadiusCircle(lat, lng);
    mapRenderer.updateTrainerPopupButtons();

    // Heading
    let heading = 0;
    const now = Date.now();
    const gpsSpeed = e?.coords?.speed;

    if (!gameState.lastMoveTimestamp) gameState.lastMoveTimestamp = now;
    if (!gameState.lastMovePosition) gameState.lastMovePosition = { lat, lng };

    let distSince = 0;
    try {
      distSince = distanceMeters(lat, lng, gameState.lastMovePosition.lat, gameState.lastMovePosition.lng);
      if (distSince > config.geolocation.positionThreshold) {
        gameState.lastMoveTimestamp = now;
        gameState.lastMovePosition = { lat, lng };
      }
    } catch (err) { logger.error('Movement distance error', err); }

    let useMovementHeading = false;
    if (now - gameState.lastMoveTimestamp < config.geolocation.movementTimeout) {
      if (typeof gpsSpeed === 'number' && gpsSpeed > 1 && gameState.lastGpsForHeading) {
        heading = calculateAzimuth(gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng, lat, lng);
        useMovementHeading = true;
      } else if (gameState.lastGpsForHeading) {
        const md = distanceMeters(lat, lng, gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng);
        if (md > config.geolocation.positionThreshold) {
          heading = calculateAzimuth(gameState.lastGpsForHeading.lat, gameState.lastGpsForHeading.lng, lat, lng);
          useMovementHeading = true;
        }
      }
      if (useMovementHeading) gameState.lastMovementHeading = heading;
    } else {
      heading = gameState.lastKnownHeading || 0;
      useMovementHeading = false;
    }

    gameState.lastGpsForHeading = { lat, lng };
    if (!useMovementHeading && typeof gameState.lastKnownHeading === 'number') heading = gameState.lastKnownHeading;

    mapRenderer.updateCompassMarker(lat, lng, heading);

    if (config.ui.showPlayerSprite) {
      if (!playerRenderer.playerMarker) {
        playerRenderer.initPlayerSprite(lat, lng, 'm');
      } else {
        playerRenderer.playerMarker.setLatLng([lat, lng]);
        const moving = distSince > config.geolocation.positionThreshold;
        if (moving) playerRenderer.updatePosition(lat, lng);
        else playerRenderer.updateDirection(heading);
      }
    }

    // Auto-centering
    if (!gameState.hasCenteredOnUser) {
      gameState.map.setView([lat, lng], gameState.map.getZoom());
      gameState.hasCenteredOnUser = true;
      gameState.isCenteringActive = true;
    } else if (gameState.shouldRecenterOnNextLocation) {
      gameState.map.setView([lat, lng], gameState.map.getZoom());
      gameState.shouldRecenterOnNextLocation = false;
      mapRenderer.hideCenterButton();
      gameState.isCenteringActive = true;
    } else if (gameState.isCenteringActive) {
      gameState.map.setView([lat, lng], gameState.map.getZoom());
    }
  } catch (err) {
    logger.error('UI update error', err);
  }
}

// =========================
// Storage sync (batched)
// =========================
async function performStorageSync() {
  await storageMutex.acquire();
  try {
    if (config.timeEvents.enabled && !gameState.disableStorageSync) {
      await syncGameStateWithStorage();
      logger.debug(`Storage sync: ${gameState.pokemons.length} Pokemon`);
    }
  } catch (e) {
    logger.error('Storage sync error', e);
  } finally { storageMutex.release(); }
}
const debouncedStorageSync = debounce(performStorageSync, 2000);

// =========================
function startPokemonMovement() {
  if (gameState.pokemonMoveInterval) clearInterval(gameState.pokemonMoveInterval);
  gameState.pokemonMoveInterval = setInterval(() => {
    try {
      gameState.pokemons.forEach((p) => {
        if (isNaN(p.spawnLat) || isNaN(p.spawnLng)) {
          logger.error('Invalid Pokemon coordinates:', p.id);
          return;
        }
        const offset = { lat: (Math.random() - 0.5) * 0.0002, lng: (Math.random() - 0.5) * 0.0002 };
        const newLat = p.spawnLat + offset.lat;
        const newLng = p.spawnLng + offset.lng;
        animatePokemonMove(p, newLat, newLng);
      });
    } catch (e) { logger.error('Pokemon movement error', e); }
  }, config.pokemon.moveInterval);
}

function animatePokemonMove(pokemon, newLat, newLng) {
  const steps = config.pokemon.moveAnimationSteps;
  const oldLat = pokemon.lat, oldLng = pokemon.lng;
  let step = 0;
  function animate() {
    try {
      step++;
      const t = step / steps;
      pokemon.lat = oldLat + (newLat - oldLat) * t;
      pokemon.lng = oldLng + (newLng - oldLng) * t;
      const entry = gameState.pokemonMarkers.get(pokemon.id);
      if (entry && entry.marker) entry.marker.setLatLng([pokemon.lat, pokemon.lng]);
      if (step < steps) requestAnimationFrame(animate);
      else {
        pokemon.lat = newLat; pokemon.lng = newLng;
        const e2 = gameState.pokemonMarkers.get(pokemon.id);
        if (e2 && e2.marker) e2.marker.setLatLng([pokemon.lat, pokemon.lng]);
      }
    } catch (err) { logger.error('Animate Pokemon error', err); }
  }
  animate();
}

// =========================
// Device orientation
// =========================
function setupDeviceOrientation() {
  if (window.DeviceOrientationEvent) {
    const handler = throttle((event) => {
      try {
        let heading = null;
        if (event.absolute && event.alpha !== null) heading = event.alpha;
        else if (event.webkitCompassHeading !== undefined) heading = event.webkitCompassHeading;
        else if (event.alpha !== null) heading = 360 - event.alpha;
        if (typeof heading === 'number' && !isNaN(heading)) gameState.lastKnownHeading = heading;
      } catch (e) { logger.error('Orientation error', e); }
    }, 100);
    window.addEventListener('deviceorientationabsolute' in window ? 'deviceorientationabsolute' : 'deviceorientation', handler, true);
  }
}

// =========================
// GPS watch control
// =========================
let globalStopGpsWatch = null;
let gpsWatchPaused = false;

function handleGeolocationError(err) {
  let message = 'GPS-Fehler aufgetreten.';
  switch (err.code) {
    case 1: message = 'GPS-Zugriff verweigert. Bitte Standortzugriff erlauben.'; break;
    case 2: message = 'Standort nicht verfügbar. Bitte später erneut versuchen.'; break;
    case 3: message = 'GPS-Timeout. Bitte erneut versuchen.'; break;
  }
  if (!gameState.lastGpsErrorTime || Date.now() - gameState.lastGpsErrorTime > 30000) {
    alert(message); // wichtige Fehler mit Alert
    gameState.lastGpsErrorTime = Date.now();
  }
}

function startLocationWatch(mapRenderer, pokemonSpawner) {
  if (appState.gpsWatchActive && typeof globalStopGpsWatch === 'function') {
    globalStopGpsWatch();
    appState.gpsWatchActive = false;
  }
  globalStopGpsWatch = startWatchPosition((pos, err) => {
    if (gpsWatchPaused) {
      logger.debug('GPS ignored - mock mode');
      return;
    }
    if (err) { logger.error('Geolocation error', err); handleGeolocationError(err); return; }

    const throttled = throttle(() => {
      debouncedLocationHandler({
        latlng: { lat: pos.coords.latitude, lng: pos.coords.longitude },
        coords: pos.coords,
        timestamp: pos.timestamp
      });
    }, 1000);
    throttled();
  });
  appState.gpsWatchActive = true;
}

function pauseGpsWatch() { gpsWatchPaused = true; logger.debug('GPS pause (mock)'); }
function resumeGpsWatch() { gpsWatchPaused = false; logger.debug('GPS resume (mock)'); }

// =========================
// Time-events setup
// =========================
async function setupTimeEventHandlers() {
  try {
    if (!config.timeEvents.enabled) return;

    const timeEventResult = await initializeTimeEvents();
    gameState.loadedFromStorage = false;

    if (!timeEventResult.isNewSlot && timeEventResult.spawns.length > 0) {
      if (gameState.pokemons.length > 0) gameState.pokemons = [];
      if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
        await gameState.loadPokedexData();
      }
      const recreated = appState.currentPokemonSpawner.recreateStoredPokemons(timeEventResult.spawns);
      gameState.loadedFromStorage = true;

      await handleStoredPokemonPositioning(recreated);
    }

    // Note: setupHourChangeTimer() was removed as it was unused

    gameState.events.on('pokemonsUpdated', () => {
      debouncedStorageSync();
    });
  } catch (e) {
    logger.error('Time events setup error', e);
  }
}

function getCurrentPosition() {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) { reject(new Error('Geolocation not supported')); return; }
    navigator.geolocation.getCurrentPosition(
      (position) => resolve({ lat: position.coords.latitude, lng: position.coords.longitude }),
      (error) => reject(error),
      { timeout: config.geolocation.timeout, maximumAge: config.geolocation.maximumAge }
    );
  });
}

function calculateAveragePosition(arr) {
  let a = 0, b = 0;
  arr.forEach(p => { a += p.spawnLat; b += p.spawnLng; });
  return { lat: a / arr.length, lng: b / arr.length };
}

async function handleStoredPokemonPositioning(recreated) {
  if (recreated.length === 0) return;
  try {
    const pos = await getCurrentPosition();
    const avg = calculateAveragePosition(recreated);
    const d = distanceMeters(pos.lat, pos.lng, avg.lat, avg.lng);
    if (d > config.pokemon.spawnRadius) {
      gameState.pokemons = [];
      gameState.loadedFromStorage = false;
      gameState.lastSpawnLatLng = null;
      // Clear markers when Pokemon are too far away
      if (appState.currentMapRenderer) {
        appState.currentMapRenderer.clearAllPokemonMarkers();
      }
      await clearSpawnsFromStorage();
    } else {
      gameState.lastSpawnLatLng = avg;
    }
    if (appState.currentMapRenderer) await appState.currentMapRenderer.renderPokemons(true);
  } catch (e) {
    const first = recreated[0];
    gameState.lastSpawnLatLng = { lat: first.spawnLat, lng: first.spawnLng };
    gameState.gpsTimeoutOccurred = true;
  }
}

// =========================
// Catch & Trainer
// =========================
window.catchPokemonById = async function(id) {
  await spawnMutex.acquire();
  try {
    if (!gameState.lastUserLatLng) { alert('Position nicht verfügbar!'); return; }
    const pokemon = gameState.pokemons.find(p => p.id === id);
    if (!pokemon) { alert('Pokemon nicht gefunden!'); return; }
    const d = distanceMeters(gameState.lastUserLatLng.lat, gameState.lastUserLatLng.lng, pokemon.lat, pokemon.lng);
    if (d > config.pokemon.catchRadius) {
      alert(`Zu weit entfernt! (${Math.round(d)}m, max. ${config.pokemon.catchRadius}m)`);
      return;
    }
    await addEncounter({ id: pokemon.id, name: pokemon.name, level: pokemon.level });
    if (config.timeEvents.enabled) await removePokemonFromStorage(id);
    gameState.removePokemon(id);
    // Use MapRenderer method to clear the marker
    if (appState.currentMapRenderer) {
      appState.currentMapRenderer.clearPokemonMarker(id);
      await appState.currentMapRenderer.renderPokemons();
    }
    if (config.timeEvents.enabled) debouncedStorageSync();
    alert('Pokemon gefangen! Gehe zu Encounters, um zu kämpfen.');
  } catch (e) {
    logger.error('Catch error', e);
    alert('Fehler beim Fangen. Bitte erneut versuchen.');
  } finally {
    spawnMutex.release();
  }
};

window.challengeTrainer = async function(trainerId) {
  await trainerMutex.acquire();
  try {
    const trainer = gameState.trainers.find(t => t.id === trainerId);
    if (!trainer) { alert('Trainer nicht gefunden!'); return; }
    if (!gameState.lastUserLatLng) { alert('Position nicht verfügbar!'); return; }
    const d = distanceMeters(gameState.lastUserLatLng.lat, gameState.lastUserLatLng.lng, trainer.lat, trainer.lng);
    if (d > config.trainers.challengeRadius) {
      alert(`Zu weit entfernt! (${Math.round(d)}m, max. ${config.trainers.challengeRadius}m)`);
      return;
    }
    await pokemonManager.initialize();
    const team = pokemonManager.getTeamPokemon();
    if (!team || team.length !== 6) {
      alert('Du brauchst genau 6 Pokémon im Team für einen Trainerkampf!'); return;
    }
    if (!trainer.team || trainer.team.length !== 6) {
      alert('Trainer hat kein vollständiges Team!'); return;
    }
    const { trainerBattleScreen } = await import('./ui/TrainerBattleScreen.js');
    await trainerBattleScreen.startBattle(trainer);
  } catch (e) {
    logger.error('Trainer battle error', e);
    alert('Fehler beim Starten des Trainerkampfs: ' + e.message);
  } finally {
    trainerMutex.release();
  }
};

// =========================
// Init
// =========================
async function initApp() {
  if (appState.isInitializing || appState.isInitialized) {
    logger.warn('Init already in progress/completed'); return;
  }
  appState.isInitializing = true;

  try {
    await gameState.initialize();
    await pokemonManager.initialize();

    const mapRenderer = new MapRenderer();
    mapRenderer.initMap();
    appState.currentMapRenderer = mapRenderer;
    window.mapRenderer = mapRenderer;

    const pokemonSpawner = new PokemonSpawner();
    appState.currentPokemonSpawner = pokemonSpawner;
    window.pokemonSpawner = pokemonSpawner;

    window.pauseGpsWatch = pauseGpsWatch;
    window.resumeGpsWatch = resumeGpsWatch;

    if (config.ui.showPlayerSprite) {
      const link = document.createElement('link');
      link.rel = 'stylesheet'; link.href = './styles/player.css';
      document.head.appendChild(link);
      const { defaultCenter } = config.map;
      playerRenderer.initPlayerSprite(defaultCenter[0], defaultCenter[1], 'm');
      playerRenderer.updateDirection('n');
      playerRenderer.setMoving(false);
    }

    await initializeTimeEvents();
    await setupTimeEventHandlers();

    fabManager.initialize();
    window.fabManager = fabManager;

    setTimeout(() => mapRenderer.setupMapPanListener(), 1000);

    await requestLocationPermissions();
    await keepScreenAwake();

    startLocationWatch(mapRenderer, pokemonSpawner);
    startPokemonMovement();
    setupDeviceOrientation();
    registerMainMapBackButtonHandler();

    appState.isInitialized = true;
    logger.info('App initialized successfully');
  } catch (e) {
    logger.error('Critical init error', e);
    alert('Fehler beim Initialisieren der App: ' + e.message);
  } finally {
    appState.isInitializing = false;
  }
}

// =========================
// Bootstrap
// =========================
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initApp, 0);
} else {
  document.addEventListener('DOMContentLoaded', initApp);
}

// =========================
// GPS Mock bridge
// =========================
window.addEventListener('mockGPSUpdate', (event) => {
  const position = event.detail;
  if (appState.currentMapRenderer && appState.currentPokemonSpawner && appState.isInitialized) {
    debouncedLocationHandler({
      latlng: { lat: position.coords.latitude, lng: position.coords.longitude },
      coords: position.coords,
      timestamp: Date.now()
    });
  } else {
    logger.warn('Mock GPS received before init');
  }
});

// =========================
// Debug helpers
// =========================
window.testGPS = {
  enableMock: (lat, lng) => { gpsMock.enableMock(lat, lng); },
  enableMapClick: () => gpsMock.enableMapClickMode(),
  disableMapClick: () => gpsMock.disableMapClickMode(),
  disable: () => gpsMock.disableMock(),
  moveTo: (lat, lng) => gpsMock.moveTo(lat, lng),
  simulateWalk: () => {
    if (!gpsMock.mockEnabled) { console.warn('GPS Mock zuerst aktivieren: testGPS.enableMock(lat, lng)'); return; }
    const route = [
      {lat: 51.96, lng: 7.62},
      {lat: 51.965, lng: 7.625},
      {lat: 51.97, lng: 7.63},
      {lat: 51.975, lng: 7.635}
    ];
    gpsMock.setRoute(route); gpsMock.startWalkMode();
    let i = 0;
    gpsMock.walkInterval = setInterval(() => {
      gpsMock.nextRoutePoint(); i++;
      if (i >= route.length) {
        clearInterval(gpsMock.walkInterval);
        gpsMock.stopWalkMode();
        console.log('🚶 Walk-Simulation beendet');
      }
    }, 2000);
  },
  getAppState: () => ({
    isInitialized: appState.isInitialized,
    isInitializing: appState.isInitializing,
    gpsWatchActive: appState.gpsWatchActive,
    pokemonCount: gameState.pokemons.length,
    trainerCount: gameState.trainers.length,
    lastLocationUpdate: gpsFilter?.lastAccepted?.t || null,
    lastSpeedMps: gpsFilter.lastSpeedMps
  }),
  help: () => {
    console.log(`
🧪 GPS Mock & Debug:
  testGPS.enableMock(lat, lng)
  testGPS.disable()
  testGPS.moveTo(lat, lng)
  testGPS.enableMapClick()
  testGPS.disableMapClick()
  testGPS.simulateWalk()
  testGPS.getAppState()
`);
  }
};

window.debugSpawn = {
  checkState: () => {
    const info = {
      pokemonCount: gameState.pokemons.length,
      trainerCount: gameState.trainers.length,
      lastSpawn: gameState.lastSpawnLatLng,
      lastAcceptedGPS: gpsFilter.lastAccepted,
      lastSpeedMps: gpsFilter.lastSpeedMps,
      inJumpCooldown: isInJumpCooldown()
    };
    logger.debug('=== SPAWN STATE DEBUG ===', info);
    return info;
  },
  forceSpawn: async (lat, lng, standard, landuse) => {
    if (!appState.isInitialized) { console.warn('App not initialized'); return; }
    await requestPokemonSpawn(appState.currentPokemonSpawner, lat || 51.96, lng || 7.62, 'debug_force', { standard, landuse });
  },
  simulateMovement: (lat, lng) => {
    if (!appState.isInitialized) { console.warn('App not initialized'); return; }
    debouncedLocationHandler({
      latlng: { lat, lng },
      coords: { latitude: lat, longitude: lng, accuracy: 10 },
      timestamp: Date.now()
    });
  },
  clearAllPokemon: () => {
    gameState.pokemons = [];
    // Use MapRenderer method to clear all Pokemon markers
    if (appState.currentMapRenderer) {
      appState.currentMapRenderer.clearAllPokemonMarkers();
      appState.currentMapRenderer.renderPokemons(true);
    }
    console.log('All Pokemon cleared');
  },
  clearAllTrainers: () => {
    gameState.trainers = [];
    // Use MapRenderer method to clear all trainer markers
    if (appState.currentMapRenderer) {
      appState.currentMapRenderer.clearAllTrainerMarkers();
    }
    console.log('All trainers cleared');
  },
  clearAllMarkers: () => {
    // Use MapRenderer method to clear all markers
    if (appState.currentMapRenderer) {
      appState.currentMapRenderer.clearAllMarkers();
    }
    console.log('All markers cleared');
  }
};

window.getStorageInfo = async function() {
  const info = await storageService.getStorageInfo();
  console.log('Storage Info:', info);
  console.log(`Total Pokemon in game: ${gameState.pokemons.length}`);
  return info;
};

window.cleanupStorage = async function() {
  await storageService.cleanupOldData();
  console.log('Storage cleanup completed');
};

window.getPerformanceInfo = function() {
  return {
    memory: performance.memory ? {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
    } : 'Not available',
    timing: { loadComplete: Math.round(performance.now()) + ' ms' },
    appState: window.testGPS.getAppState()
  };
};
