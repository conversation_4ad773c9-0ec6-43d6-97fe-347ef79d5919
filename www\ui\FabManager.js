// ui/FabManager.js
// Manager for Floating Action Buttons (FABs)

import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import { getChainIndexForLocation, getHighestRarityForChain } from '../pokemon-grid.js';
import { fabSubmenuManager } from './FabSubmenuManager.js';
import { config } from '../config.js';
import { openBattleScreen } from './BattleScreen.js';
import { clearSpawnsFromStorage } from '../capacitor/time-events.js';
import { PokemonSpawner } from '../services/pokemon-spawner.js';
import { MapRenderer } from '../services/map-renderer.js';

export class FabManager {
  constructor() {
    this.fabBar = null;
    this.buttons = {};
  }

  /**
   * Initialize the FAB manager
   */
  initialize() {
    this.ensureFabBar();
    this.setupFabs();

    // Initialize submenu manager
    fabSubmenuManager.initialize();

    logger.debug('FabManager initialized');
  }



  /**
   * Ensure the FAB bar exists
   * @returns {HTMLElement} - The FAB bar
   */
  ensureFabBar() {
    let fabBar = document.querySelector('.fab-bar');
    if (!fabBar) {
      fabBar = document.createElement('div');
      fabBar.className = 'fab-bar';

      // Stelle sicher, dass die Grid-Eigenschaften korrekt gesetzt sind
      fabBar.style.display = 'grid';
      fabBar.style.gridTemplateColumns = 'repeat(4, 1fr)';
      fabBar.style.gridTemplateRows = '1fr';
      fabBar.style.justifyItems = 'center';
      fabBar.style.alignItems = 'center';

      document.body.appendChild(fabBar);
      logger.debug('Created new FAB bar with grid layout');
    }
    this.fabBar = fabBar;
    return fabBar;
  }

  /**
   * Create a FAB button
   * @param {Object} options - Button options
   * @returns {HTMLElement} - The created button
   */
  createFabButton(options) {
    const { id, title, icon, onClick, alwaysVisible = true } = options;

    // Check if button already exists
    let fab = document.getElementById(id);
    if (fab) {
      logger.debug(`FAB button with ID ${id} already exists`);
      return fab;
    }

    const fabBar = this.ensureFabBar();
    fab = document.createElement('button');

    fab.id = id;
    fab.title = title;
    fab.className = 'fab-btn';
    fab.innerHTML = `<img src="${icon}" alt="${title}" class="icon-svg" />`;
    fab.onclick = onClick;

    // Setze die Grid-Position basierend auf der Button-ID
    const position = this.getGridPosition(id);
    fab.style.gridColumn = position.column;
    fab.style.gridRow = position.row;

    if (!alwaysVisible) {
      fab.style.display = 'none';
    }

    fabBar.appendChild(fab);
    this.buttons[id] = fab;
    logger.debug(`Created FAB button with ID ${id} at grid position ${fab.style.gridColumn}`);

    return fab;
  }

  /**
   * Get grid position for a FAB button ID
   * @param {string} id - Button ID
   * @returns {Object} - Object with column and row properties
   */
  getGridPosition(id) {
    const positions = {
      'battle-fab': { column: '1', row: '1' },
      'center-fab': { column: '2', row: '1' },
      'debug-fab': { column: '3', row: '1' },
      'submenu-fab': { column: '4', row: '1' }
    };
    return positions[id] || { column: '1', row: '1' };
  }

  /**
   * Setup all FAB buttons
   */
  setupFabs() {
    // Submenu - opens submenu
    this.createFabButton({
      id: 'submenu-fab',
      title: 'Open Submenu',
      icon: './icons/materialicons/menu.svg',
      onClick: () => fabSubmenuManager.toggleSubmenu()
    });

    // Battle - opens battle screen
    this.createFabButton({
      id: 'battle-fab',
      title: 'Pokémon Battle starten',
      icon: './icons/materialicons/swords.svg',
      onClick: openBattleScreen
    });

    // Debug
    this.createFabButton({
      id: 'debug-fab',
      title: 'Debug Modus umschalten',
      icon: './icons/materialicons/bug.svg',
      onClick: this.toggleDebugMode.bind(this)
    });

    // GPS/Center (initially hidden)
    this.createFabButton({
      id: 'center-fab',
      title: 'Karte auf meine Position zentrieren',
      icon: './icons/materialicons/mylocation.svg',
      onClick: this.centerMap.bind(this),
      alwaysVisible: false
    });
  }

  /**
   * Toggle debug mode
   */
  toggleDebugMode() {
    // Make sure submenu is closed before toggling debug mode
    if (fabSubmenuManager.isActive) {
      fabSubmenuManager.closeSubmenu();
    }

    const newDebugMode = gameState.toggleDebugMode();

    if (newDebugMode) {
      this.showDebugInfo();
    } else {
      this.hideDebugInfo();
    }

    logger.debug(`Debug mode toggled: ${newDebugMode}`);
  }

  /**
   * Show debug information
   */
  async showDebugInfo() {
    // Debug info box
    let debugInfoDiv = document.getElementById('debug-info');
    let isNewDebugDiv = false;

    if (!debugInfoDiv) {
      isNewDebugDiv = true;
      debugInfoDiv = document.createElement('div');
      debugInfoDiv.id = 'debug-info';
      debugInfoDiv.style.position = 'fixed';
      debugInfoDiv.style.left = '32px';
      debugInfoDiv.style.bottom = '104px';
      debugInfoDiv.style.background = 'rgba(255,253,231,0.95)';
      debugInfoDiv.style.color = '#333';
      debugInfoDiv.style.fontWeight = 'bold';
      debugInfoDiv.style.padding = '8px 16px';
      debugInfoDiv.style.borderRadius = '8px';
      debugInfoDiv.style.zIndex = 9999;
      debugInfoDiv.style.display = 'flex';
      debugInfoDiv.style.flexDirection = 'column';
      debugInfoDiv.style.gap = '8px';
    }

    // Only clear and recreate content if this is a new debug div
    if (isNewDebugDiv) {
      // Clear existing content
      debugInfoDiv.innerHTML = '';

      // Add info text
      const infoText = document.createElement('div');
      infoText.id = 'debug-info-text';
      infoText.textContent = `Pokémon auf der Map: ${gameState.pokemons.length} | Familien: ${gameState.chainsList.length}`;
      debugInfoDiv.appendChild(infoText);

      // Add reset button
      const resetButton = document.createElement('button');
      resetButton.textContent = 'Spawns zurücksetzen';
      resetButton.style.padding = '4px 8px';
      resetButton.style.fontSize = '12px';
      resetButton.style.backgroundColor = '#ff5722';
      resetButton.style.color = 'white';
      resetButton.style.border = 'none';
      resetButton.style.borderRadius = '4px';
      resetButton.style.cursor = 'pointer';
      resetButton.style.fontWeight = 'bold';
      resetButton.style.marginRight = '5px';
      resetButton.onclick = () => this.clearAllSpawnsAndRespawn();
      debugInfoDiv.appendChild(resetButton);

      // Add cache stats button
      const cacheStatsButton = document.createElement('button');
      cacheStatsButton.textContent = 'Cache Stats';
      cacheStatsButton.style.padding = '4px 8px';
      cacheStatsButton.style.fontSize = '12px';
      cacheStatsButton.style.backgroundColor = '#2196f3';
      cacheStatsButton.style.color = 'white';
      cacheStatsButton.style.border = 'none';
      cacheStatsButton.style.borderRadius = '4px';
      cacheStatsButton.style.cursor = 'pointer';
      cacheStatsButton.style.fontWeight = 'bold';
      cacheStatsButton.style.marginRight = '5px';
      cacheStatsButton.onclick = () => this.showCacheStats();
      debugInfoDiv.appendChild(cacheStatsButton);

      // Add clear Overpass cache button
      const clearCacheButton = document.createElement('button');
      clearCacheButton.textContent = 'Overpass Cache leeren';
      clearCacheButton.style.padding = '4px 8px';
      clearCacheButton.style.fontSize = '12px';
      clearCacheButton.style.backgroundColor = '#ff5722';
      clearCacheButton.style.color = 'white';
      clearCacheButton.style.border = 'none';
      clearCacheButton.style.borderRadius = '4px';
      clearCacheButton.style.cursor = 'pointer';
      clearCacheButton.style.fontWeight = 'bold';
      clearCacheButton.style.marginRight = '5px';
      clearCacheButton.onclick = () => this.clearOverpassCache();
      debugInfoDiv.appendChild(clearCacheButton);

      document.body.appendChild(debugInfoDiv);
    } else {
      // Update existing debug info text
      const infoText = debugInfoDiv.querySelector('#debug-info-text');
      if (infoText) {
        infoText.textContent = `Pokémon auf der Map: ${gameState.pokemons.length} | Familien: ${gameState.chainsList.length}`;
      }
    }

    // Update or create yellow circle
    this.updateDebugRadiusCircle();

    // Grid lines and labels
    this.showDebugGrid();

    // Show landuse polygons
    try {
      // Use the global mapRenderer instance
      if (window.mapRenderer) {
        window.mapRenderer.showLandusePolygons();
      }
    } catch (error) {
      logger.error('Error showing landuse polygons in debug mode:', error);
    }
  }

  /**
   * Hide debug information
   */
  async hideDebugInfo() {
    // Debug info box
    const debugInfoDiv = document.getElementById('debug-info');
    if (debugInfoDiv) {
      debugInfoDiv.remove();
    }

    // Yellow circle
    if (gameState.debugRadiusCircle && gameState.map) {
      gameState.map.removeLayer(gameState.debugRadiusCircle);
      gameState.debugRadiusCircle = null;
    }

    // Grid lines and labels
    this.hideDebugGrid();

    // Hide landuse polygons
    try {
      // Use the global mapRenderer instance
      if (window.mapRenderer) {
        window.mapRenderer.hideLandusePolygons();
      }
    } catch (error) {
      logger.error('Error hiding landuse polygons in debug mode:', error);
    }
  }

  /**
   * Show debug grid
   */
  showDebugGrid() {
    const { map } = gameState;
    if (!map) return;

    // Remove previous grid (if any)
    this.hideDebugGrid();

    gameState.debugGridLayers = [];
    gameState.debugGridLabels = [];

    const bounds = map.getBounds();
    const center = map.getCenter();
    const metersPerDegLat = 111000;
    const cellDegLat = config.grid.cellSizeMeters / metersPerDegLat;
    const metersPerDegLon = metersPerDegLat * Math.cos(center.lat * Math.PI / 180);
    const cellDegLon = config.grid.cellSizeMeters / metersPerDegLon;
    const minLat = bounds.getSouth();
    const maxLat = bounds.getNorth();
    const minLng = bounds.getWest();
    const maxLng = bounds.getEast();
    const startLat = Math.floor(minLat / cellDegLat) * cellDegLat;
    const endLat = Math.ceil(maxLat / cellDegLat) * cellDegLat;
    const startLng = Math.floor(minLng / cellDegLon) * cellDegLon;
    const endLng = Math.ceil(maxLng / cellDegLon) * cellDegLon;

    // Vertical lines
    for (let lng = startLng; lng <= endLng; lng += cellDegLon) {
      const latlngs = [[minLat, lng], [maxLat, lng]];
      const line = L.polyline(latlngs, {
        color: 'rgba(33,150,243,0.3)',
        weight: 2,
        interactive: false,
        dashArray: '4,6',
        pane: 'overlayPane'
      }).addTo(map);
      gameState.debugGridLayers.push(line);
    }

    // Horizontal lines
    for (let lat = startLat; lat <= endLat; lat += cellDegLat) {
      const latlngs = [[lat, minLng], [lat, maxLng]];
      const line = L.polyline(latlngs, {
        color: 'rgba(33,150,243,0.3)',
        weight: 2,
        interactive: false,
        dashArray: '4,6',
        pane: 'overlayPane'
      }).addTo(map);
      gameState.debugGridLayers.push(line);
    }

    // Labels
    for (let lat = startLat; lat < endLat; lat += cellDegLat) {
      for (let lng = startLng; lng < endLng; lng += cellDegLon) {
        const centerLat = lat + cellDegLat / 2;
        const centerLng = lng + cellDegLon / 2;
        const info = this.getChainInfoForGrid(centerLat, centerLng);
        if (!info) continue;

        // Add rarity information to the family names
        const famNames = info.family.map(p => p.de || p.name).join('<br>');
        const rarityText = `<span class="debug-grid-rarity ${info.rarity}">${info.rarity}</span>`;
        const labelContent = `${famNames}<br>${rarityText}`;

        const label = L.marker([centerLat, centerLng], {
          icon: L.divIcon({
            className: 'debug-grid-label',
            html: `<span class='debug-grid-label-text'>${labelContent}</span>`
          }),
          interactive: false
        }).addTo(map);
        gameState.debugGridLabels.push(label);
      }
    }
  }

  /**
   * Hide debug grid
   */
  hideDebugGrid() {
    const { map } = gameState;
    if (!map) return;

    if (gameState.debugGridLayers) {
      gameState.debugGridLayers.forEach(line => map.removeLayer(line));
      gameState.debugGridLayers = [];
    }

    if (gameState.debugGridLabels) {
      gameState.debugGridLabels.forEach(marker => map.removeLayer(marker));
      gameState.debugGridLabels = [];
    }
  }

  /**
   * Get chain info for a grid cell
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @returns {Object|null} - Chain info or null if not found
   */
  getChainInfoForGrid(lat, lon) {
    if (!gameState.chainsList.length) return null;

    const chainIdx = getChainIndexForLocation(lat, lon, gameState.chainsList.length);
    const chainId = gameState.chainsList[chainIdx];

    // All Pokemon of this family from the Pokedex
    const family = gameState.pokedexData.filter(p => p.evolution_chain_id == chainId);

    // Get the highest priority rarity in the family
    const rarity = getHighestRarityForChain(chainId);

    return { chainIdx, chainId, family, rarity };
  }

  /**
   * Center the map on the user's location
   */
  centerMap() {
    // Make sure submenu is closed before centering
    if (fabSubmenuManager.isActive) {
      fabSubmenuManager.closeSubmenu();
    }

    gameState.shouldRecenterOnNextLocation = true;
    gameState.isCenteringActive = true;

    if (gameState.lastUserLatLng && gameState.map) {
      gameState.map.setView(
        [gameState.lastUserLatLng.lat, gameState.lastUserLatLng.lng],
        gameState.map.getZoom()
      );

      logger.debug('Map centered on user location');
    } else {
      logger.debug('Could not center map: no user location available');
    }

    this.hideButton('center-fab');
  }

  /**
   * Show a button by ID
   * @param {string} id - Button ID
   */
  showButton(id) {
    try {
      const btn = document.getElementById(id);
      if (btn) {
        btn.style.display = 'flex';

        // Stelle sicher, dass der Button in der richtigen Grid-Zelle ist
        const position = this.getGridPosition(id);
        btn.style.gridColumn = position.column;
        btn.style.gridRow = position.row;

        logger.debug(`Button ${id} shown with correct grid position`);
      } else {
        // Statt einer Warnung nur ein Debug-Log, um keine Fehler zu verursachen
        logger.debug(`Button with ID ${id} not found, cannot show it`);
      }
    } catch (e) {
      // Fehler abfangen und nur als Debug-Log ausgeben
      logger.debug(`Error showing button ${id}: ${e.message}`);
    }
  }

  /**
   * Hide a button by ID
   * @param {string} id - Button ID
   */
  hideButton(id) {
    try {
      const btn = document.getElementById(id);
      if (btn) {
        btn.style.display = 'none';
        logger.debug(`Button ${id} hidden`);
      } else {
        // Statt einer Warnung nur ein Debug-Log, um keine Fehler zu verursachen
        logger.debug(`Button with ID ${id} not found, cannot hide it`);
      }
    } catch (e) {
      // Fehler abfangen und nur als Debug-Log ausgeben
      logger.debug(`Error hiding button ${id}: ${e.message}`);
    }
  }

  /**
   * Hide all FAB buttons
   * This is useful when opening overlays to prevent errors
   */
  hideAllButtons() {
    try {
      // Hide the FAB bar
      const fabBar = document.querySelector('.fab-bar');
      if (fabBar) {
        fabBar.style.display = 'none';
        logger.debug('All FAB buttons hidden (bar hidden)');
      } else {
        // Statt einer Warnung nur ein Debug-Log, um keine Fehler zu verursachen
        logger.debug('FAB bar not found, cannot hide all buttons');
      }
    } catch (e) {
      // Fehler abfangen und nur als Debug-Log ausgeben
      logger.debug(`Error hiding all FAB buttons: ${e.message}`);
    }
  }

  /**
   * Show all FAB buttons
   * This is useful when closing overlays
   */
  showAllButtons() {
    try {
      // Show the FAB bar
      const fabBar = document.querySelector('.fab-bar');
      if (fabBar) {
        // Stelle sicher, dass die FAB-Bar als Grid angezeigt wird
        fabBar.style.display = 'grid';

        // Stelle sicher, dass die Grid-Eigenschaften korrekt gesetzt sind
        fabBar.style.gridTemplateColumns = 'repeat(4, 1fr)';
        fabBar.style.gridTemplateRows = '1fr';
        fabBar.style.justifyItems = 'center';
        fabBar.style.alignItems = 'center';

        // Stelle sicher, dass die Buttons in den richtigen Grid-Zellen sind
        const battleFab = document.getElementById('battle-fab');
        if (battleFab) {
          battleFab.style.gridColumn = '1';
          battleFab.style.gridRow = '1';
        }

        const centerFab = document.getElementById('center-fab');
        if (centerFab) {
          centerFab.style.gridColumn = '2';
          centerFab.style.gridRow = '1';
        }

        const debugFab = document.getElementById('debug-fab');
        if (debugFab) {
          debugFab.style.gridColumn = '3';
          debugFab.style.gridRow = '1';
        }

        const submenuFab = document.getElementById('submenu-fab');
        if (submenuFab) {
          submenuFab.style.gridColumn = '4';
          submenuFab.style.gridRow = '1';
        }

        logger.debug('All FAB buttons shown with correct grid layout');
      } else {
        // Statt einer Warnung nur ein Debug-Log, um keine Fehler zu verursachen
        logger.debug('FAB bar not found, cannot show all buttons');
      }
    } catch (e) {
      // Fehler abfangen und nur als Debug-Log ausgeben
      logger.debug(`Error showing all FAB buttons: ${e.message}`);
    }
  }

  /**
   * Clear all spawns and respawn new Pokemon
   */
  async clearAllSpawnsAndRespawn() {
    try {
      logger.debug('Starting spawn reset process...');

      // 1. Clear all Pokemon from game state
      gameState.pokemons = [];

      // 2. Remove all Pokemon markers from map
      if (gameState.pokemonMarkers && gameState.map) {
        gameState.pokemonMarkers.forEach(entry => {
          gameState.map.removeLayer(entry.marker);
        });
        gameState.pokemonMarkers.clear();
      }

      // 3. Clear spawn storage
      if (config.timeEvents && config.timeEvents.enabled) {
        const cleared = await clearSpawnsFromStorage();
        logger.debug(`Cleared spawns from storage: ${cleared ? 'success' : 'failed'}`);
      }

      // 4. Reset spawn location and storage flags to force new spawns
      gameState.lastSpawnLatLng = null;
      gameState.loadedFromStorage = false;
      logger.debug('Reset spawn flags: lastSpawnLatLng=null, loadedFromStorage=false');

      // 5. Clear any existing GPS stabilization timer
      if (gameState.gpsStabilizationTimer) {
        clearTimeout(gameState.gpsStabilizationTimer);
        gameState.gpsStabilizationTimer = null;
        logger.debug('Cleared existing GPS stabilization timer');
      }

      // 6. Temporarily disable storage sync during reset
      gameState.disableStorageSync = true;
      logger.debug('Disabled storage sync during reset');

      // 7. Get current user location for spawning
      const userLocation = gameState.lastUserLatLng;
      if (!userLocation) {
        logger.warn('No user location available for respawning');
        alert('Keine GPS-Position verfügbar für neue Spawns');
        // Re-enable storage sync before returning
        gameState.disableStorageSync = false;
        return;
      }

      // 8. Spawn new Pokemon
      logger.debug(`Spawning new Pokemon at location: ${userLocation.lat}, ${userLocation.lng}`);

      // Create Pokemon spawner instance
      const pokemonSpawner = new PokemonSpawner();

      // Pre-load landuse cache once to avoid race conditions between spawn methods
      logger.debug('[CACHE_DEBUG] Pre-loading landuse cache before spawning...');
      await pokemonSpawner.loadLanduseDataForArea(userLocation.lat, userLocation.lng, pokemonSpawner.spawnRadius);
      logger.debug(`[CACHE_DEBUG] Pre-loaded cache with ${pokemonSpawner.landuseCache?.data?.features?.length || 0} features`);

      // Spawn both types of Pokemon in parallel (cache is already loaded)
      const [standardSpawns, landuseSpawns] = await Promise.all([
        pokemonSpawner.spawnRandomPokemons(
          userLocation.lat,
          userLocation.lng,
          config.pokemon.spawnBatchSize
        ),
        pokemonSpawner.spawnLanduseSpecialPokemons(
          userLocation.lat,
          userLocation.lng,
          config.pokemon.landuseSpecialBatchSize
        )
      ]);

      logger.debug(`[CACHE_DEBUG] Spawned ${standardSpawns.length} standard + ${landuseSpawns.length} landuse Pokemon`);

      // 9. Update spawn location
      gameState.lastSpawnLatLng = { lat: userLocation.lat, lng: userLocation.lng };

      // 10. Re-enable storage sync
      gameState.disableStorageSync = false;
      logger.debug('Re-enabled storage sync after reset');

      // 11. Render new Pokemon on map
      const mapRenderer = new MapRenderer();
      mapRenderer.renderPokemons(true);

      // 12. Update debug info display
      this.updateDebugInfoDisplay();

      logger.debug(`Spawn reset complete. New Pokemon count: ${gameState.pokemons.length}`);

    } catch (error) {
      logger.error('Error during spawn reset:', error);
      alert('Fehler beim Zurücksetzen der Spawns');
    }
  }

  /**
   * Update the debug info display with current counts
   */
  updateDebugInfoDisplay() {
    const debugInfoDiv = document.getElementById('debug-info');
    if (debugInfoDiv && gameState.debugMode) {
      const infoText = debugInfoDiv.querySelector('#debug-info-text');
      if (infoText) {
        infoText.textContent = `Pokémon auf der Map: ${gameState.pokemons.length} | Familien: ${gameState.chainsList.length}`;
      }
    }
  }

  /**
   * Update or create the debug radius circle
   */
  updateDebugRadiusCircle() {
    // Remove existing circle if it exists
    if (gameState.debugRadiusCircle && gameState.map) {
      gameState.map.removeLayer(gameState.debugRadiusCircle);
      gameState.debugRadiusCircle = null;
    }

    // Create new circle if debug mode is active and we have a spawn location
    if (gameState.debugMode && gameState.lastSpawnLatLng && gameState.map) {
      gameState.debugRadiusCircle = L.circle([gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng], {
        radius: 250,
        color: '#fbc02d',
        fillColor: '#fffde7',
        fillOpacity: 0.25,
        weight: 3,
        interactive: false
      }).addTo(gameState.map);
    }
  }

  /**
   * Show cache statistics
   */
  async showCacheStats() {
    try {
      // Import cache functions dynamically
      const { getCacheStats, cleanupExpiredCache } = await import('../overpass-landuse.js');

      // Clean up expired entries first
      const cleanedCount = cleanupExpiredCache();

      // Get current stats
      const stats = getCacheStats();

      const message = `Overpass API Cache Statistiken:

Gesamt Einträge: ${stats.totalEntries}
Gültige Einträge: ${stats.validEntries}
Abgelaufene Einträge: ${stats.expiredEntries}
Maximum Einträge: ${stats.maxEntries}
Cache Dauer: ${stats.cacheDurationMinutes} Minuten

${cleanedCount > 0 ? `\n${cleanedCount} abgelaufene Einträge wurden automatisch entfernt.` : ''}`;

      alert(message);
      logger.debug('Cache stats displayed:', stats);
    } catch (error) {
      logger.error('Error showing cache stats:', error);
      alert('Fehler beim Anzeigen der Cache-Statistiken');
    }
  }

  /**
   * Clear the Overpass API cache
   */
  async clearOverpassCache() {
    try {
      // Import enhanced cache clearing function
      const { clearOverpassCache } = await import('../overpass-landuse.js');

      // Clear all cached Overpass landuse data
      const result = clearOverpassCache();

      // Show detailed feedback to user
      const message = `🗑️ Overpass Cache geleert!\n\n` +
                     `💾 Memory Cache: ${result.memoryCacheCleared} Einträge\n` +
                     `💿 localStorage: ${result.localStorageCacheCleared} Einträge\n` +
                     `📊 Gesamt: ${result.totalCleared} Einträge\n\n` +
                     `🔄 Beim nächsten Spawn werden neue Daten geladen.`;

      alert(message);
      logger.info(`[CACHE_CLEAR] ✅ Cleared ${result.totalCleared} total cache entries`);

      // Trigger performance testing on next spawn
      logger.info(`[CACHE_CLEAR] 🧪 Performance testing will be triggered on next spawn cycle`);

    } catch (error) {
      logger.error('Error clearing Overpass cache:', error);
      alert('❌ Fehler beim Löschen des Overpass Caches.');
    }
  }
}

// Export a singleton instance
export const fabManager = new FabManager();
